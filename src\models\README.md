# 字典数据全局状态管理

本文档说明如何使用dva对字典数据进行全局缓存管理。

## 概述

字典管理系统使用dva进行全局状态管理，提供了三种字典类型的数据缓存：
- 区域字典 (region)
- 类型字典 (type)  
- 关系字典 (relation)

## 核心文件

### 1. Model文件
- `src/models/dictionary.ts` - 字典数据的dva model

### 2. 工具文件
- `src/utils/dictionary.ts` - 字典数据的工具函数和Hook

### 3. 示例文件
- `src/pages/Examples/DictionaryUsage.tsx` - 使用示例页面

## 基本使用

### 1. 在组件中使用字典数据

```tsx
import { useDictUtils } from '@/utils/dictionary';

const MyComponent = () => {
  const {
    regionTree,
    typeTree,
    relationTree,
    loading,
    loadAllDict,
    findDictById,
    getDictName,
  } = useDictUtils();

  useEffect(() => {
    // 加载所有字典数据
    loadAllDict();
  }, []);

  return (
    <div>
      {/* 使用字典数据 */}
    </div>
  );
};
```

### 2. 在表单中使用字典数据

```tsx
import { useDictUtils } from '@/utils/dictionary';

const FormComponent = () => {
  const { convertToTreeSelectOptions, convertToSelectOptions } = useDictUtils();

  return (
    <Form>
      {/* TreeSelect 使用 */}
      <Form.Item name="region">
        <TreeSelect
          treeData={convertToTreeSelectOptions('region')}
          placeholder="请选择区域"
        />
      </Form.Item>

      {/* Select 使用 */}
      <Form.Item name="type">
        <Select
          options={convertToSelectOptions('type')}
          placeholder="请选择类型"
        />
      </Form.Item>
    </Form>
  );
};
```

## API 参考

### useDictUtils Hook

#### 数据状态
- `regionTree: RegionDict[]` - 区域字典树形数据
- `typeTree: TypeDict[]` - 类型字典树形数据
- `relationTree: RelationshipDict[]` - 关系字典树形数据
- `regionFlat: RegionDict[]` - 区域字典扁平数据
- `typeFlat: TypeDict[]` - 类型字典扁平数据
- `relationFlat: RelationshipDict[]` - 关系字典扁平数据
- `loading: { region: boolean; type: boolean; relation: boolean }` - 加载状态

#### 加载方法
- `loadAllDict(force?: boolean)` - 加载所有字典数据
- `loadDictByType(type: DictType, force?: boolean)` - 加载指定类型字典数据
- `loadRegionDict(force?: boolean)` - 加载区域字典
- `loadTypeDict(force?: boolean)` - 加载类型字典
- `loadRelationDict(force?: boolean)` - 加载关系字典

#### 查找方法
- `findDictById(type: DictType, id: number)` - 根据ID查找字典项
- `findDictByCode(type: DictType, code: string)` - 根据编码查找字典项
- `getDictName(type: DictType, id: number)` - 获取字典项名称
- `getDictPath(type: DictType, id: number)` - 获取字典项路径

#### 转换方法
- `convertToTreeSelectOptions(type: DictType, data?, excludeId?)` - 转换为TreeSelect选项
- `convertToSelectOptions(type: DictType, onlyEnabled?)` - 转换为Select选项

#### 工具方法
- `getChildrenIds(type: DictType, parentId: number)` - 获取子项ID列表
- `isChildOf(type: DictType, childId: number, parentId: number)` - 检查父子关系
- `getDictLevel(type: DictType, id: number)` - 获取层级深度
- `filterDict(type: DictType, filters)` - 筛选字典数据
- `refreshDict(type: DictType)` - 刷新指定类型数据
- `clearCache(type?: DictType)` - 清空缓存

### DictFormatter 工具类

- `formatDictText(type, item, showCode?)` - 格式化显示文本
- `formatStatus(status)` - 格式化状态文本
- `formatSort(sort)` - 格式化排序值

## 缓存机制

### 自动缓存
- 数据加载后自动缓存5分钟
- 5分钟内重复请求直接返回缓存数据
- 可通过 `force=true` 参数强制刷新

### 手动管理
```tsx
// 强制刷新数据
await loadDictByType('region', true);

// 清空指定类型缓存
clearCache('region');

// 清空所有缓存
clearCache();
```

## 最佳实践

### 1. 页面初始化
```tsx
useEffect(() => {
  // 只加载需要的字典类型
  loadDictByType('region');
}, []);
```

### 2. 表单验证
```tsx
const validateRegion = (rule: any, value: number) => {
  const region = findDictById('region', value);
  if (!region || region.status !== 1) {
    return Promise.reject('请选择有效的区域');
  }
  return Promise.resolve();
};
```

### 3. 数据展示
```tsx
const RegionDisplay = ({ regionId }: { regionId: number }) => {
  const { getDictName, getDictPath } = useDictUtils();
  
  return (
    <div>
      <div>名称: {getDictName('region', regionId)}</div>
      <div>路径: {getDictPath('region', regionId).join(' > ')}</div>
    </div>
  );
};
```

### 4. 条件筛选
```tsx
// 筛选启用的一级区域
const topLevelRegions = filterDict('region', {
  status: 1,
  level: 1
});

// 关键词搜索
const searchResults = filterDict('type', {
  keyword: '建筑'
});
```

## 注意事项

1. **性能优化**: 大量数据时建议使用虚拟滚动
2. **错误处理**: 网络错误时会显示错误消息
3. **数据一致性**: 修改字典数据后需要手动刷新缓存
4. **内存管理**: 长时间不使用时可以清空缓存

## 示例页面

访问 `/examples/dictionary-usage` 查看完整的使用示例。
