/**
 * @file 字典工具函数
 * @description 提供字典数据的便捷访问和转换方法
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import { useModel } from '@umijs/max';
import type { RegionDict, RelationshipDict, TypeDict } from '@/services/dictionary';
import type { DictType } from '@/models/dictionary';

// 树形选择器数据格式
export interface TreeSelectOption {
  title: string;
  value: number;
  key: number;
  children?: TreeSelectOption[];
}

// 下拉选择器数据格式
export interface SelectOption {
  label: string;
  value: number;
  disabled?: boolean;
}

/**
 * 字典工具Hook
 * 提供字典数据的便捷访问方法
 */
export const useDictUtils = () => {
  const dictionary = useModel('dictionary');

  /**
   * 将树形字典数据转换为TreeSelect组件需要的格式
   */
  const convertToTreeSelectOptions = (
    type: DictType,
    data?: (RegionDict | TypeDict | RelationshipDict)[],
    excludeId?: number
  ): TreeSelectOption[] => {
    const treeData = data || (
      type === 'region' ? dictionary.regionTree :
      type === 'type' ? dictionary.typeTree :
      dictionary.relationTree
    );

    const getNameField = (type: DictType) => {
      return type === 'region' ? 'regionName' :
             type === 'type' ? 'typeName' :
             'relationName';
    };

    const getCodeField = (type: DictType) => {
      return type === 'region' ? 'regionCode' :
             type === 'type' ? 'typeCode' :
             'relationCode';
    };

    const convertNode = (node: any): TreeSelectOption => {
      const nameField = getNameField(type);
      const codeField = getCodeField(type);
      
      return {
        title: `${node[nameField]} (${node[codeField]})`,
        value: node.id,
        key: node.id,
        children: node.children && node.children.length > 0
          ? node.children
              .filter((child: any) => child.id !== excludeId)
              .map(convertNode)
          : undefined,
      };
    };

    return treeData
      .filter(node => node.id !== excludeId)
      .map(convertNode);
  };

  /**
   * 将扁平字典数据转换为Select组件需要的格式
   */
  const convertToSelectOptions = (
    type: DictType,
    onlyEnabled = true
  ): SelectOption[] => {
    const flatData = type === 'region' ? dictionary.regionFlat :
                    type === 'type' ? dictionary.typeFlat :
                    dictionary.relationFlat;

    const getNameField = (type: DictType) => {
      return type === 'region' ? 'regionName' :
             type === 'type' ? 'typeName' :
             'relationName';
    };

    const getCodeField = (type: DictType) => {
      return type === 'region' ? 'regionCode' :
             type === 'type' ? 'typeCode' :
             'relationCode';
    };

    const nameField = getNameField(type);
    const codeField = getCodeField(type);

    return flatData
      .filter(item => !onlyEnabled || item.status === 1)
      .map(item => ({
        label: `${(item as any)[nameField]} (${(item as any)[codeField]})`,
        value: item.id,
        disabled: item.status !== 1,
      }));
  };

  /**
   * 获取字典项的完整路径（面包屑）
   */
  const getDictPath = (type: DictType, id: number): string[] => {
    const flatData = type === 'region' ? dictionary.regionFlat :
                    type === 'type' ? dictionary.typeFlat :
                    dictionary.relationFlat;

    const getNameField = (type: DictType) => {
      return type === 'region' ? 'regionName' :
             type === 'type' ? 'typeName' :
             'relationName';
    };

    const nameField = getNameField(type);
    const path: string[] = [];
    
    const findPath = (currentId: number): boolean => {
      const item = flatData.find(d => d.id === currentId);
      if (!item) return false;
      
      path.unshift((item as any)[nameField]);
      
      if (item.parentId) {
        return findPath(item.parentId);
      }
      
      return true;
    };

    findPath(id);
    return path;
  };

  /**
   * 获取字典项的所有子项ID
   */
  const getChildrenIds = (type: DictType, parentId: number): number[] => {
    const flatData = type === 'region' ? dictionary.regionFlat :
                    type === 'type' ? dictionary.typeFlat :
                    dictionary.relationFlat;

    const childrenIds: number[] = [];
    
    const findChildren = (pid: number) => {
      flatData.forEach(item => {
        if (item.parentId === pid) {
          childrenIds.push(item.id);
          findChildren(item.id);
        }
      });
    };

    findChildren(parentId);
    return childrenIds;
  };

  /**
   * 检查字典项是否为另一个字典项的子项
   */
  const isChildOf = (type: DictType, childId: number, parentId: number): boolean => {
    const flatData = type === 'region' ? dictionary.regionFlat :
                    type === 'type' ? dictionary.typeFlat :
                    dictionary.relationFlat;

    const child = flatData.find(item => item.id === childId);
    if (!child || !child.parentId) return false;

    if (child.parentId === parentId) return true;
    
    return isChildOf(type, child.parentId, parentId);
  };

  /**
   * 获取字典项的层级深度
   */
  const getDictLevel = (type: DictType, id: number): number => {
    const flatData = type === 'region' ? dictionary.regionFlat :
                    type === 'type' ? dictionary.typeFlat :
                    dictionary.relationFlat;

    let level = 0;
    let currentId = id;

    while (currentId) {
      const item = flatData.find(d => d.id === currentId);
      if (!item) break;
      
      level++;
      currentId = item.parentId || 0;
    }

    return level;
  };

  /**
   * 根据多个条件筛选字典项
   */
  const filterDict = (
    type: DictType,
    filters: {
      keyword?: string;
      status?: number;
      parentId?: number;
      level?: number;
    }
  ) => {
    const flatData = type === 'region' ? dictionary.regionFlat :
                    type === 'type' ? dictionary.typeFlat :
                    dictionary.relationFlat;

    const getNameField = (type: DictType) => {
      return type === 'region' ? 'regionName' :
             type === 'type' ? 'typeName' :
             'relationName';
    };

    const getCodeField = (type: DictType) => {
      return type === 'region' ? 'regionCode' :
             type === 'type' ? 'typeCode' :
             'relationCode';
    };

    const nameField = getNameField(type);
    const codeField = getCodeField(type);

    return flatData.filter(item => {
      // 关键词筛选
      if (filters.keyword) {
        const keyword = filters.keyword.toLowerCase();
        const name = ((item as any)[nameField] || '').toLowerCase();
        const code = ((item as any)[codeField] || '').toLowerCase();
        if (!name.includes(keyword) && !code.includes(keyword)) {
          return false;
        }
      }

      // 状态筛选
      if (filters.status !== undefined && item.status !== filters.status) {
        return false;
      }

      // 父级筛选
      if (filters.parentId !== undefined && item.parentId !== filters.parentId) {
        return false;
      }

      // 层级筛选
      if (filters.level !== undefined && getDictLevel(type, item.id) !== filters.level) {
        return false;
      }

      return true;
    });
  };

  return {
    // 数据转换
    convertToTreeSelectOptions,
    convertToSelectOptions,
    
    // 路径和关系
    getDictPath,
    getChildrenIds,
    isChildOf,
    getDictLevel,
    
    // 筛选和查找
    filterDict,
    
    // 直接访问字典数据和方法
    ...dictionary,
  };
};

/**
 * 字典数据格式化工具函数
 */
export const DictFormatter = {
  /**
   * 格式化字典项显示文本
   */
  formatDictText: (
    type: DictType,
    item: RegionDict | TypeDict | RelationshipDict,
    showCode = true
  ): string => {
    const getName = (item: any) => {
      return type === 'region' ? item.regionName :
             type === 'type' ? item.typeName :
             item.relationName;
    };

    const getCode = (item: any) => {
      return type === 'region' ? item.regionCode :
             type === 'type' ? item.typeCode :
             item.relationCode;
    };

    const name = getName(item);
    const code = getCode(item);

    return showCode ? `${name} (${code})` : name;
  },

  /**
   * 格式化状态文本
   */
  formatStatus: (status: number): { text: string; color: string } => {
    return status === 1 
      ? { text: '启用', color: 'green' }
      : { text: '禁用', color: 'red' };
  },

  /**
   * 格式化排序值
   */
  formatSort: (sort: number): string => {
    return sort.toString().padStart(3, '0');
  },
};

export default useDictUtils;
