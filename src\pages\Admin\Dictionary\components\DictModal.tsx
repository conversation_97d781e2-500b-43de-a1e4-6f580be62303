/**
 * @file 字典编辑模态框组件
 * @description 封装字典添加/编辑的模态框，包含表单和确认/取消操作
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import type { FormInstance } from 'antd';
import { Modal } from 'antd';
import React from 'react';
import type { DictType, TreeSelectData } from '../dict-types';
import { DictForm } from './DictForm';

interface DictModalProps {
  visible: boolean;
  title: string;
  loading: boolean;
  form: FormInstance;
  type: DictType;
  parentOptions: TreeSelectData[];
  onOk: () => void;
  onCancel: () => void;
}

export const DictModal: React.FC<DictModalProps> = ({
  visible,
  title,
  loading,
  form,
  type,
  parentOptions,
  onOk,
  onCancel,
}) => {
  return (
    <Modal
      title={title}
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      width={600}
      destroyOnClose
      confirmLoading={loading}
    >
      <DictForm form={form} type={type} parentOptions={parentOptions} />
    </Modal>
  );
};
