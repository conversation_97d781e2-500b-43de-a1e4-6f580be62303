/**
 * @file 字典数据管理Hook
 * @description 管理字典数据的加载、缓存和状态，包括区域、类型、关系三种字典的数据操作
 * 现在使用dva model进行全局状态管理
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import { useModel } from '@umijs/max';
import {
  getAllRegionDict,
  getAllRelationshipDict,
  getAllTypeDict,
} from '@/services/dictionary';
import { message } from 'antd';
import { useCallback, useState } from 'react';
import { MESSAGES } from '../constants';
import type {
  DictType,
  TreeSelectData,
} from '../dict-types';

export const useDictData = () => {
  // 使用dva model获取全局字典数据
  const dictionary = useModel('dictionary');
  const [parentOptions, setParentOptions] = useState<TreeSelectData[]>([]);

  // 加载区域字典数据
  const loadRegionData = useCallback(async () => {
    try {
      await dictionary.loadRegionDict();
    } catch (error) {
      message.error(MESSAGES.loadError.region);
    }
  }, [dictionary]);

  // 加载类型字典数据
  const loadTypeData = useCallback(async () => {
    try {
      await dictionary.loadTypeDict();
    } catch (error) {
      message.error(MESSAGES.loadError.type);
    }
  }, [dictionary]);

  // 加载关系字典数据
  const loadRelationData = useCallback(async () => {
    try {
      await dictionary.loadRelationDict();
    } catch (error) {
      message.error(MESSAGES.loadError.relation);
    }
  }, [dictionary]);

  // 根据类型加载数据
  const loadData = useCallback(
    (type: DictType) => {
      switch (type) {
        case 'region':
          loadRegionData();
          break;
        case 'type':
          loadTypeData();
          break;
        case 'relation':
          loadRelationData();
          break;
        default:
          break;
      }
    },
    [loadRegionData, loadTypeData, loadRelationData],
  );

  // 加载父级选项数据
  const loadParentOptions = useCallback(
    async (type: DictType, excludeId?: number) => {
      try {
        let response;
        switch (type) {
          case 'region':
            response = await getAllRegionDict();
            break;
          case 'type':
            response = await getAllTypeDict();
            break;
          case 'relation':
            response = await getAllRelationshipDict();
            break;
          default:
            return;
        }

        if (response.errCode === 0 && response.data) {
          // 转换为TreeSelect需要的格式，并排除当前编辑的项（避免循环引用）
          const convertToTreeSelectData = (items: any[]): TreeSelectData[] => {
            return items
              .filter((item) => item.id !== excludeId) // 排除当前编辑的项
              .map((item) => ({
                title: `${
                  item[
                    type === 'region'
                      ? 'regionName'
                      : type === 'type'
                      ? 'typeName'
                      : 'relationName'
                  ]
                } (${
                  item[
                    type === 'region'
                      ? 'regionCode'
                      : type === 'type'
                      ? 'typeCode'
                      : 'relationCode'
                  ]
                })`,
                value: item.id,
                key: item.id,
                children: item.children
                  ? convertToTreeSelectData(item.children)
                  : undefined,
              }));
          };

          setParentOptions(convertToTreeSelectData(response.data));
        }
      } catch (error) {
        console.error('加载父级选项失败:', error);
      }
    },
    [],
  );

  // 获取当前类型的数据
  const getCurrentData = useCallback(
    (type: DictType) => {
      switch (type) {
        case 'region':
          return dictionary.regionTree;
        case 'type':
          return dictionary.typeTree;
        case 'relation':
          return dictionary.relationTree;
        default:
          return [];
      }
    },
    [dictionary.regionTree, dictionary.typeTree, dictionary.relationTree],
  );

  // 获取加载状态
  const getLoading = useCallback(
    (type?: DictType) => {
      if (type) {
        return dictionary.loading[type];
      }
      return dictionary.loading.region || dictionary.loading.type || dictionary.loading.relation;
    },
    [dictionary.loading],
  );

  return {
    // 数据状态
    regionData: dictionary.regionTree,
    typeData: dictionary.typeTree,
    relationData: dictionary.relationTree,
    loading: getLoading(),
    parentOptions,

    // 方法
    loadData,
    loadParentOptions,
    getCurrentData,

    // 单独的加载方法
    loadRegionData,
    loadTypeData,
    loadRelationData,

    // 新增方法
    getLoading,
  };
};
