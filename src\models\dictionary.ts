/**
 * @file 字典数据全局状态管理
 * @description 使用dva管理字典数据的全局缓存，方便其他页面引用
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import {
  getAllRegionDict,
  getAllRelationshipDict,
  getAllTypeDict,
  getRegionDictTree,
  getRelationshipDictTree,
  getTypeDictTree,
  type RegionDict,
  type RelationshipDict,
  type TypeDict,
} from '@/services/dictionary';
import { message } from 'antd';
import { useCallback, useState } from 'react';

// 字典状态接口
export interface DictionaryState {
  // 原始数据（树形结构）
  regionTree: RegionDict[];
  typeTree: TypeDict[];
  relationTree: RelationshipDict[];
  
  // 扁平化数据（用于快速查找）
  regionFlat: RegionDict[];
  typeFlat: TypeDict[];
  relationFlat: RelationshipDict[];
  
  // 加载状态
  loading: {
    region: boolean;
    type: boolean;
    relation: boolean;
  };
  
  // 最后更新时间
  lastUpdated: {
    region?: number;
    type?: number;
    relation?: number;
  };
}

// 字典类型
export type DictType = 'region' | 'type' | 'relation';

// 扁平化树形数据的工具函数
const flattenTreeData = <T extends { id: number; children?: T[] }>(
  treeData: T[],
): T[] => {
  const result: T[] = [];
  
  const traverse = (nodes: T[]) => {
    nodes.forEach((node) => {
      result.push(node);
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  
  traverse(treeData);
  return result;
};

// 字典数据管理Hook
const useDictionary = () => {
  const [state, setState] = useState<DictionaryState>({
    regionTree: [],
    typeTree: [],
    relationTree: [],
    regionFlat: [],
    typeFlat: [],
    relationFlat: [],
    loading: {
      region: false,
      type: false,
      relation: false,
    },
    lastUpdated: {},
  });

  // 更新状态的通用方法
  const updateState = useCallback((updates: Partial<DictionaryState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  }, []);

  // 设置加载状态
  const setLoading = useCallback((type: DictType, loading: boolean) => {
    updateState({
      loading: {
        ...state.loading,
        [type]: loading,
      },
    });
  }, [state.loading, updateState]);

  // 加载区域字典数据
  const loadRegionDict = useCallback(async (force = false) => {
    // 如果不是强制刷新且数据已存在且在5分钟内，则不重新加载
    const now = Date.now();
    const lastUpdate = state.lastUpdated.region;
    if (!force && state.regionTree.length > 0 && lastUpdate && (now - lastUpdate) < 5 * 60 * 1000) {
      return state.regionTree;
    }

    try {
      setLoading('region', true);
      const response = await getRegionDictTree();
      
      if (response.errCode === 0 && response.data) {
        const treeData = response.data;
        const flatData = flattenTreeData(treeData);
        
        updateState({
          regionTree: treeData,
          regionFlat: flatData,
          lastUpdated: {
            ...state.lastUpdated,
            region: now,
          },
        });
        
        return treeData;
      } else {
        message.error('加载区域字典失败');
        return [];
      }
    } catch (error) {
      console.error('加载区域字典失败:', error);
      message.error('加载区域字典失败');
      return [];
    } finally {
      setLoading('region', false);
    }
  }, [state.regionTree, state.lastUpdated, setLoading, updateState]);

  // 加载类型字典数据
  const loadTypeDict = useCallback(async (force = false) => {
    const now = Date.now();
    const lastUpdate = state.lastUpdated.type;
    if (!force && state.typeTree.length > 0 && lastUpdate && (now - lastUpdate) < 5 * 60 * 1000) {
      return state.typeTree;
    }

    try {
      setLoading('type', true);
      const response = await getTypeDictTree();
      
      if (response.errCode === 0 && response.data) {
        const treeData = response.data;
        const flatData = flattenTreeData(treeData);
        
        updateState({
          typeTree: treeData,
          typeFlat: flatData,
          lastUpdated: {
            ...state.lastUpdated,
            type: now,
          },
        });
        
        return treeData;
      } else {
        message.error('加载类型字典失败');
        return [];
      }
    } catch (error) {
      console.error('加载类型字典失败:', error);
      message.error('加载类型字典失败');
      return [];
    } finally {
      setLoading('type', false);
    }
  }, [state.typeTree, state.lastUpdated, setLoading, updateState]);

  // 加载关系字典数据
  const loadRelationDict = useCallback(async (force = false) => {
    const now = Date.now();
    const lastUpdate = state.lastUpdated.relation;
    if (!force && state.relationTree.length > 0 && lastUpdate && (now - lastUpdate) < 5 * 60 * 1000) {
      return state.relationTree;
    }

    try {
      setLoading('relation', true);
      const response = await getRelationshipDictTree();
      
      if (response.errCode === 0 && response.data) {
        const treeData = response.data;
        const flatData = flattenTreeData(treeData);
        
        updateState({
          relationTree: treeData,
          relationFlat: flatData,
          lastUpdated: {
            ...state.lastUpdated,
            relation: now,
          },
        });
        
        return treeData;
      } else {
        message.error('加载关系字典失败');
        return [];
      }
    } catch (error) {
      console.error('加载关系字典失败:', error);
      message.error('加载关系字典失败');
      return [];
    } finally {
      setLoading('relation', false);
    }
  }, [state.relationTree, state.lastUpdated, setLoading, updateState]);

  // 根据类型加载字典数据
  const loadDictByType = useCallback(async (type: DictType, force = false) => {
    switch (type) {
      case 'region':
        return await loadRegionDict(force);
      case 'type':
        return await loadTypeDict(force);
      case 'relation':
        return await loadRelationDict(force);
      default:
        return [];
    }
  }, [loadRegionDict, loadTypeDict, loadRelationDict]);

  // 加载所有字典数据
  const loadAllDict = useCallback(async (force = false) => {
    const promises = [
      loadRegionDict(force),
      loadTypeDict(force),
      loadRelationDict(force),
    ];
    
    try {
      await Promise.all(promises);
    } catch (error) {
      console.error('加载字典数据失败:', error);
    }
  }, [loadRegionDict, loadTypeDict, loadRelationDict]);

  // 根据ID查找字典项
  const findDictById = useCallback((type: DictType, id: number) => {
    const flatData = type === 'region' ? state.regionFlat : 
                    type === 'type' ? state.typeFlat : 
                    state.relationFlat;
    
    return flatData.find(item => item.id === id);
  }, [state.regionFlat, state.typeFlat, state.relationFlat]);

  // 根据编码查找字典项
  const findDictByCode = useCallback((type: DictType, code: string) => {
    const flatData = type === 'region' ? state.regionFlat : 
                    type === 'type' ? state.typeFlat : 
                    state.relationFlat;
    
    const codeField = type === 'region' ? 'regionCode' : 
                     type === 'type' ? 'typeCode' : 
                     'relationCode';
    
    return flatData.find(item => (item as any)[codeField] === code);
  }, [state.regionFlat, state.typeFlat, state.relationFlat]);

  // 获取字典项的显示名称
  const getDictName = useCallback((type: DictType, id: number) => {
    const item = findDictById(type, id);
    if (!item) return '';
    
    const nameField = type === 'region' ? 'regionName' : 
                     type === 'type' ? 'typeName' : 
                     'relationName';
    
    return (item as any)[nameField] || '';
  }, [findDictById]);

  // 刷新指定类型的字典数据
  const refreshDict = useCallback(async (type: DictType) => {
    return await loadDictByType(type, true);
  }, [loadDictByType]);

  // 清空缓存
  const clearCache = useCallback((type?: DictType) => {
    if (type) {
      updateState({
        [`${type}Tree`]: [],
        [`${type}Flat`]: [],
        lastUpdated: {
          ...state.lastUpdated,
          [type]: undefined,
        },
      });
    } else {
      updateState({
        regionTree: [],
        typeTree: [],
        relationTree: [],
        regionFlat: [],
        typeFlat: [],
        relationFlat: [],
        lastUpdated: {},
      });
    }
  }, [state.lastUpdated, updateState]);

  return {
    // 状态数据
    ...state,
    
    // 加载方法
    loadRegionDict,
    loadTypeDict,
    loadRelationDict,
    loadDictByType,
    loadAllDict,
    
    // 查找方法
    findDictById,
    findDictByCode,
    getDictName,
    
    // 工具方法
    refreshDict,
    clearCache,
  };
};

export default useDictionary;
