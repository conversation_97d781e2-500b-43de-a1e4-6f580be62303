/**
 * @file 字典使用示例页面
 * @description 展示如何在其他页面中使用dva管理的字典数据
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import { useDictUtils } from '@/utils/dictionary';
import {
  Button,
  Card,
  Col,
  Descriptions,
  Form,
  Row,
  Select,
  Space,
  Spin,
  Tag,
  TreeSelect,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

interface FormData {
  region?: number;
  type?: number;
  relation?: number;
  parentRegion?: number;
}

const DictionaryUsageExample: React.FC = () => {
  const [form] = Form.useForm<FormData>();
  const [selectedItems, setSelectedItems] = useState<{
    region?: any;
    type?: any;
    relation?: any;
  }>({});

  // 使用字典工具Hook
  const {
    // 数据状态
    regionTree,
    typeTree,
    relationTree,
    regionFlat,
    typeFlat,
    relationFlat,
    loading,
    
    // 加载方法
    loadAllDict,
    loadDictByType,
    
    // 查找方法
    findDictById,
    getDictName,
    getDictPath,
    
    // 转换方法
    convertToTreeSelectOptions,
    convertToSelectOptions,
    
    // 筛选方法
    filterDict,
  } = useDictUtils();

  // 页面初始化时加载所有字典数据
  useEffect(() => {
    loadAllDict();
  }, [loadAllDict]);

  // 处理表单值变化
  const handleFormChange = (changedValues: Partial<FormData>, allValues: FormData) => {
    const newSelectedItems: typeof selectedItems = {};
    
    if (allValues.region) {
      newSelectedItems.region = findDictById('region', allValues.region);
    }
    if (allValues.type) {
      newSelectedItems.type = findDictById('type', allValues.type);
    }
    if (allValues.relation) {
      newSelectedItems.relation = findDictById('relation', allValues.relation);
    }
    
    setSelectedItems(newSelectedItems);
  };

  // 刷新字典数据
  const handleRefresh = async (type?: 'region' | 'type' | 'relation') => {
    if (type) {
      await loadDictByType(type, true);
    } else {
      await loadAllDict(true);
    }
  };

  // 获取启用状态的字典项数量
  const getEnabledCount = (type: 'region' | 'type' | 'relation') => {
    const flatData = type === 'region' ? regionFlat : 
                    type === 'type' ? typeFlat : 
                    relationFlat;
    return flatData.filter(item => item.status === 1).length;
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>字典数据使用示例</Title>
      <Paragraph>
        本页面展示如何在其他页面中使用dva管理的字典数据。字典数据已经全局缓存，
        可以在任何页面中通过 <Text code>useDictUtils</Text> Hook 访问。
      </Paragraph>

      <Row gutter={[16, 16]}>
        {/* 数据统计卡片 */}
        <Col span={24}>
          <Card title="字典数据统计" loading={loading.region || loading.type || loading.relation}>
            <Row gutter={16}>
              <Col span={8}>
                <Card size="small">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="区域字典">
                      <Space>
                        <Tag color="blue">总数: {regionFlat.length}</Tag>
                        <Tag color="green">启用: {getEnabledCount('region')}</Tag>
                      </Space>
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="类型字典">
                      <Space>
                        <Tag color="blue">总数: {typeFlat.length}</Tag>
                        <Tag color="green">启用: {getEnabledCount('type')}</Tag>
                      </Space>
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="关系字典">
                      <Space>
                        <Tag color="blue">总数: {relationFlat.length}</Tag>
                        <Tag color="green">启用: {getEnabledCount('relation')}</Tag>
                      </Space>
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 表单示例 */}
        <Col span={12}>
          <Card 
            title="表单中使用字典数据" 
            extra={
              <Space>
                <Button size="small" onClick={() => handleRefresh()}>
                  刷新全部
                </Button>
              </Space>
            }
          >
            <Spin spinning={loading.region || loading.type || loading.relation}>
              <Form
                form={form}
                layout="vertical"
                onValuesChange={handleFormChange}
              >
                <Form.Item label="选择区域（TreeSelect）" name="region">
                  <TreeSelect
                    placeholder="请选择区域"
                    treeData={convertToTreeSelectOptions('region')}
                    allowClear
                    showSearch
                    treeDefaultExpandAll
                  />
                </Form.Item>

                <Form.Item label="选择类型（Select）" name="type">
                  <Select
                    placeholder="请选择类型"
                    options={convertToSelectOptions('type')}
                    allowClear
                    showSearch
                  />
                </Form.Item>

                <Form.Item label="选择关系" name="relation">
                  <Select
                    placeholder="请选择关系"
                    allowClear
                    showSearch
                  >
                    {relationFlat
                      .filter(item => item.status === 1)
                      .map(item => (
                        <Option key={item.id} value={item.id}>
                          {item.relationName} ({item.relationCode})
                        </Option>
                      ))
                    }
                  </Select>
                </Form.Item>

                <Form.Item label="父级区域（排除子级）" name="parentRegion">
                  <TreeSelect
                    placeholder="请选择父级区域"
                    treeData={convertToTreeSelectOptions('region', undefined, form.getFieldValue('region'))}
                    allowClear
                    showSearch
                  />
                </Form.Item>
              </Form>
            </Spin>
          </Card>
        </Col>

        {/* 选中项详情 */}
        <Col span={12}>
          <Card title="选中项详情">
            {selectedItems.region && (
              <Card size="small" title="区域信息" style={{ marginBottom: 16 }}>
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="名称">
                    {selectedItems.region.regionName}
                  </Descriptions.Item>
                  <Descriptions.Item label="编码">
                    {selectedItems.region.regionCode}
                  </Descriptions.Item>
                  <Descriptions.Item label="路径">
                    {getDictPath('region', selectedItems.region.id).join(' > ')}
                  </Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={selectedItems.region.status === 1 ? 'green' : 'red'}>
                      {selectedItems.region.status === 1 ? '启用' : '禁用'}
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            )}

            {selectedItems.type && (
              <Card size="small" title="类型信息" style={{ marginBottom: 16 }}>
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="名称">
                    {selectedItems.type.typeName}
                  </Descriptions.Item>
                  <Descriptions.Item label="编码">
                    {selectedItems.type.typeCode}
                  </Descriptions.Item>
                  <Descriptions.Item label="路径">
                    {getDictPath('type', selectedItems.type.id).join(' > ')}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            )}

            {selectedItems.relation && (
              <Card size="small" title="关系信息">
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="名称">
                    {selectedItems.relation.relationName}
                  </Descriptions.Item>
                  <Descriptions.Item label="编码">
                    {selectedItems.relation.relationCode}
                  </Descriptions.Item>
                  <Descriptions.Item label="路径">
                    {getDictPath('relation', selectedItems.relation.id).join(' > ')}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            )}

            {!selectedItems.region && !selectedItems.type && !selectedItems.relation && (
              <Text type="secondary">请在左侧表单中选择字典项查看详情</Text>
            )}
          </Card>
        </Col>

        {/* API使用示例 */}
        <Col span={24}>
          <Card title="常用API示例">
            <Row gutter={16}>
              <Col span={8}>
                <Card size="small" title="数据查找">
                  <Paragraph>
                    <Text code>findDictById('region', 1)</Text><br/>
                    <Text code>getDictName('type', 2)</Text><br/>
                    <Text code>getDictPath('relation', 3)</Text>
                  </Paragraph>
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small" title="数据转换">
                  <Paragraph>
                    <Text code>convertToTreeSelectOptions('region')</Text><br/>
                    <Text code>convertToSelectOptions('type')</Text>
                  </Paragraph>
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small" title="数据筛选">
                  <Paragraph>
                    <Text code>filterDict('region', {'{'}keyword: '北京'{'}'})</Text><br/>
                    <Text code>filterDict('type', {'{'}status: 1{'}'})</Text>
                  </Paragraph>
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DictionaryUsageExample;
