# 字典数据dva全局缓存使用指南

## 概述

本指南介绍如何使用dva对字典数据进行全局缓存管理，以便在整个应用中方便地访问和使用字典数据。

## 架构设计

### 1. 核心组件

```
src/
├── models/
│   ├── dictionary.ts          # dva model - 字典数据状态管理
│   └── README.md             # 详细API文档
├── utils/
│   └── dictionary.ts         # 工具函数和Hook
├── pages/
│   ├── Admin/Dictionary/     # 字典管理页面
│   └── Examples/
│       └── DictionaryUsage.tsx  # 使用示例
└── services/
    └── dictionary.ts         # API服务
```

### 2. 数据流

```
API服务 → dva Model → 全局缓存 → 组件使用
```

## 快速开始

### 1. 基本使用

```tsx
import { useDictUtils } from '@/utils/dictionary';

const MyComponent = () => {
  const {
    regionTree,
    typeTree,
    relationTree,
    loading,
    loadAllDict,
  } = useDictUtils();

  useEffect(() => {
    // 页面初始化时加载字典数据
    loadAllDict();
  }, [loadAllDict]);

  if (loading.region || loading.type || loading.relation) {
    return <Spin />;
  }

  return (
    <div>
      <h3>区域数量: {regionTree.length}</h3>
      <h3>类型数量: {typeTree.length}</h3>
      <h3>关系数量: {relationTree.length}</h3>
    </div>
  );
};
```

### 2. 表单中使用

```tsx
import { useDictUtils } from '@/utils/dictionary';

const FormExample = () => {
  const [form] = Form.useForm();
  const { 
    convertToTreeSelectOptions, 
    convertToSelectOptions,
    loadAllDict 
  } = useDictUtils();

  useEffect(() => {
    loadAllDict();
  }, [loadAllDict]);

  return (
    <Form form={form}>
      {/* 树形选择器 */}
      <Form.Item name="region" label="选择区域">
        <TreeSelect
          treeData={convertToTreeSelectOptions('region')}
          placeholder="请选择区域"
          allowClear
          showSearch
          treeDefaultExpandAll
        />
      </Form.Item>

      {/* 下拉选择器 */}
      <Form.Item name="type" label="选择类型">
        <Select
          options={convertToSelectOptions('type')}
          placeholder="请选择类型"
          allowClear
          showSearch
        />
      </Form.Item>
    </Form>
  );
};
```

### 3. 数据查找和显示

```tsx
const DataDisplay = ({ regionId, typeId }: { regionId: number; typeId: number }) => {
  const { 
    findDictById, 
    getDictName, 
    getDictPath 
  } = useDictUtils();

  const region = findDictById('region', regionId);
  const typeName = getDictName('type', typeId);
  const regionPath = getDictPath('region', regionId);

  return (
    <div>
      <p>区域: {region?.regionName} ({region?.regionCode})</p>
      <p>类型: {typeName}</p>
      <p>路径: {regionPath.join(' > ')}</p>
    </div>
  );
};
```

## 高级用法

### 1. 条件筛选

```tsx
const FilterExample = () => {
  const { filterDict } = useDictUtils();

  // 筛选启用的区域
  const enabledRegions = filterDict('region', { status: 1 });

  // 关键词搜索
  const searchResults = filterDict('type', { keyword: '建筑' });

  // 筛选一级分类
  const topLevelTypes = filterDict('type', { level: 1 });

  return (
    <div>
      {enabledRegions.map(region => (
        <Tag key={region.id}>{region.regionName}</Tag>
      ))}
    </div>
  );
};
```

### 2. 缓存管理

```tsx
const CacheManagement = () => {
  const { 
    refreshDict, 
    clearCache, 
    loadDictByType 
  } = useDictUtils();

  const handleRefresh = async () => {
    // 强制刷新区域字典
    await refreshDict('region');
  };

  const handleClearCache = () => {
    // 清空所有缓存
    clearCache();
  };

  const handleLoadSpecific = async () => {
    // 只加载类型字典
    await loadDictByType('type');
  };

  return (
    <Space>
      <Button onClick={handleRefresh}>刷新区域</Button>
      <Button onClick={handleClearCache}>清空缓存</Button>
      <Button onClick={handleLoadSpecific}>加载类型</Button>
    </Space>
  );
};
```

### 3. 表单验证

```tsx
const FormValidation = () => {
  const { findDictById } = useDictUtils();

  const validateRegion = (rule: any, value: number) => {
    if (!value) return Promise.resolve();
    
    const region = findDictById('region', value);
    if (!region) {
      return Promise.reject('区域不存在');
    }
    if (region.status !== 1) {
      return Promise.reject('区域已禁用');
    }
    return Promise.resolve();
  };

  return (
    <Form>
      <Form.Item 
        name="region" 
        rules={[{ validator: validateRegion }]}
      >
        <TreeSelect placeholder="请选择区域" />
      </Form.Item>
    </Form>
  );
};
```

## 性能优化

### 1. 按需加载

```tsx
// 只加载需要的字典类型
useEffect(() => {
  if (needRegion) {
    loadDictByType('region');
  }
  if (needType) {
    loadDictByType('type');
  }
}, [needRegion, needType]);
```

### 2. 缓存策略

```tsx
// 数据自动缓存5分钟，可以配置缓存时间
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟

// 强制刷新
await loadDictByType('region', true);
```

### 3. 内存管理

```tsx
// 组件卸载时清理缓存（可选）
useEffect(() => {
  return () => {
    // 如果需要，可以在组件卸载时清理缓存
    // clearCache();
  };
}, []);
```

## 最佳实践

### 1. 错误处理

```tsx
const ErrorHandling = () => {
  const { loadAllDict } = useDictUtils();
  const [error, setError] = useState<string | null>(null);

  const handleLoad = async () => {
    try {
      setError(null);
      await loadAllDict();
    } catch (err) {
      setError('加载字典数据失败');
      console.error(err);
    }
  };

  if (error) {
    return <Alert message={error} type="error" />;
  }

  return <Button onClick={handleLoad}>加载数据</Button>;
};
```

### 2. 加载状态处理

```tsx
const LoadingState = () => {
  const { loading, regionTree } = useDictUtils();

  return (
    <Spin spinning={loading.region}>
      {regionTree.map(region => (
        <div key={region.id}>{region.regionName}</div>
      ))}
    </Spin>
  );
};
```

### 3. 数据同步

```tsx
// 字典数据更新后刷新缓存
const handleDictUpdate = async () => {
  // 更新字典数据的API调用
  await updateDictAPI();
  
  // 刷新缓存
  await refreshDict('region');
};
```

## 注意事项

1. **初始化**: 在使用字典数据前确保已加载
2. **缓存**: 数据会自动缓存5分钟，避免频繁请求
3. **更新**: 修改字典数据后需要手动刷新缓存
4. **性能**: 大量数据时考虑使用虚拟滚动
5. **错误**: 网络错误会自动显示提示消息

## 示例页面

访问管理后台的"开发示例" → "字典使用示例"页面查看完整的使用示例。

路径: `/admin/examples/dictionary-usage`
